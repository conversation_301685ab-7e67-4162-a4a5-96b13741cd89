:root {
  /* Premium Color Palette - Sophisticated & Professional */
  --color-white: rgba(255, 255, 255, 1);
  --color-black: rgba(0, 0, 0, 1);

  /* Neutral Grays - Enhanced for premium feel */
  --color-gray-50: rgba(249, 250, 251, 1);
  --color-gray-100: rgba(243, 244, 246, 1);
  --color-gray-200: rgba(229, 231, 235, 1);
  --color-gray-300: rgba(209, 213, 219, 1);
  --color-gray-400: rgba(156, 163, 175, 1);
  --color-gray-500: rgba(107, 114, 128, 1);
  --color-gray-600: rgba(75, 85, 99, 1);
  --color-gray-700: rgba(55, 65, 81, 1);
  --color-gray-800: rgba(31, 41, 55, 1);
  --color-gray-900: rgba(17, 24, 39, 1);

  /* Premium Blue Palette - Professional & Trustworthy */
  --color-blue-50: rgba(239, 246, 255, 1);
  --color-blue-100: rgba(219, 234, 254, 1);
  --color-blue-200: rgba(191, 219, 254, 1);
  --color-blue-300: rgba(147, 197, 253, 1);
  --color-blue-400: rgba(96, 165, 250, 1);
  --color-blue-500: rgba(59, 130, 246, 1);
  --color-blue-600: rgba(37, 99, 235, 1);
  --color-blue-700: rgba(29, 78, 216, 1);
  --color-blue-800: rgba(30, 64, 175, 1);
  --color-blue-900: rgba(30, 58, 138, 1);

  /* Premium Indigo Palette - Sophisticated Primary */
  --color-indigo-50: rgba(238, 242, 255, 1);
  --color-indigo-100: rgba(224, 231, 255, 1);
  --color-indigo-200: rgba(199, 210, 254, 1);
  --color-indigo-300: rgba(165, 180, 252, 1);
  --color-indigo-400: rgba(129, 140, 248, 1);
  --color-indigo-500: rgba(99, 102, 241, 1);
  --color-indigo-600: rgba(79, 70, 229, 1);
  --color-indigo-700: rgba(67, 56, 202, 1);
  --color-indigo-800: rgba(55, 48, 163, 1);
  --color-indigo-900: rgba(49, 46, 129, 1);

  /* Accent Colors - Success, Warning, Error */
  --color-emerald-500: rgba(16, 185, 129, 1);
  --color-emerald-600: rgba(5, 150, 105, 1);
  --color-amber-500: rgba(245, 158, 11, 1);
  --color-amber-600: rgba(217, 119, 6, 1);
  --color-red-500: rgba(239, 68, 68, 1);
  --color-red-600: rgba(220, 38, 38, 1);

  /* RGB versions for opacity control */
  --color-gray-900-rgb: 17, 24, 39;
  --color-gray-800-rgb: 31, 41, 55;
  --color-gray-700-rgb: 55, 65, 81;
  --color-gray-600-rgb: 75, 85, 99;
  --color-gray-500-rgb: 107, 114, 128;
  --color-gray-400-rgb: 156, 163, 175;
  --color-gray-300-rgb: 209, 213, 219;
  --color-gray-200-rgb: 229, 231, 235;
  --color-gray-100-rgb: 243, 244, 246;
  --color-gray-50-rgb: 249, 250, 251;
  --color-indigo-600-rgb: 79, 70, 229;
  --color-indigo-500-rgb: 99, 102, 241;
  --color-blue-600-rgb: 37, 99, 235;
  --color-emerald-500-rgb: 16, 185, 129;
  --color-amber-500-rgb: 245, 158, 11;
  --color-red-500-rgb: 239, 68, 68;

  /* Premium Background Gradients & Patterns */
  --gradient-primary: linear-gradient(135deg, var(--color-indigo-600) 0%, var(--color-blue-600) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-white) 100%);
  --gradient-hero: linear-gradient(135deg, var(--color-indigo-50) 0%, var(--color-blue-50) 50%, var(--color-white) 100%);
  --gradient-card: linear-gradient(145deg, var(--color-white) 0%, var(--color-gray-50) 100%);

  /* Semantic Color Tokens (Light Mode) - Premium Theme */
  --color-background: var(--color-white);
  --color-surface: var(--color-white);
  --color-surface-elevated: var(--color-gray-50);
  --color-text: var(--color-gray-900);
  --color-text-secondary: var(--color-gray-600);
  --color-text-muted: var(--color-gray-500);
  --color-primary: var(--color-indigo-600);
  --color-primary-hover: var(--color-indigo-700);
  --color-primary-active: var(--color-indigo-800);
  --color-primary-light: var(--color-indigo-50);
  --color-secondary: var(--color-gray-100);
  --color-secondary-hover: var(--color-gray-200);
  --color-secondary-active: var(--color-gray-300);
  --color-border: var(--color-gray-200);
  --color-border-light: var(--color-gray-100);
  --color-border-strong: var(--color-gray-300);
  --color-btn-primary-text: var(--color-white);
  --color-card-border: var(--color-gray-100);
  --color-card-shadow: rgba(var(--color-gray-900-rgb), 0.05);
  --color-error: var(--color-red-500);
  --color-success: var(--color-emerald-500);
  --color-warning: var(--color-amber-500);
  --color-info: var(--color-blue-500);
  --color-focus-ring: rgba(var(--color-indigo-600-rgb), 0.3);
  --color-select-caret: rgba(var(--color-gray-700-rgb), 0.8);

  /* Common style patterns */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for opacity control */
  --color-success-rgb: 33, 128, 141;
  --color-error-rgb: 192, 21, 47;
  --color-warning-rgb: 168, 75, 47;
  --color-info-rgb: 98, 108, 113;

  /* Premium Typography System */
  --font-family-display: "Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-family-base: "Inter", "SF Pro Text", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-family-mono: "JetBrains Mono", "SF Mono", ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace;

  /* Enhanced Font Scale */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 30px;
  --font-size-4xl: 36px;
  --font-size-5xl: 48px;
  --font-size-6xl: 60px;

  /* Professional Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;

  /* Refined Line Heights */
  --line-height-none: 1;
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  /* Letter Spacing */
  --letter-spacing-tighter: -0.05em;
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0em;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;

  /* Premium Spacing Scale */
  --space-0: 0;
  --space-px: 1px;
  --space-0-5: 2px;
  --space-1: 4px;
  --space-1-5: 6px;
  --space-2: 8px;
  --space-2-5: 10px;
  --space-3: 12px;
  --space-3-5: 14px;
  --space-4: 16px;
  --space-5: 20px;
  --space-6: 24px;
  --space-7: 28px;
  --space-8: 32px;
  --space-10: 40px;
  --space-12: 48px;
  --space-16: 64px;
  --space-20: 80px;
  --space-24: 96px;
  --space-32: 128px;
  --space-40: 160px;
  --space-48: 192px;
  --space-56: 224px;
  --space-64: 256px;

  /* Modern Border Radius */
  --radius-none: 0;
  --radius-sm: 4px;
  --radius-base: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 20px;
  --radius-3xl: 24px;
  --radius-full: 9999px;

  /* Premium Shadow System */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.05);
  --shadow-glow: 0 0 0 1px rgba(var(--color-indigo-600-rgb), 0.05), 0 1px 3px 0 rgba(var(--color-indigo-600-rgb), 0.1);

  /* Enhanced Animation System */
  --duration-75: 75ms;
  --duration-100: 100ms;
  --duration-150: 150ms;
  --duration-200: 200ms;
  --duration-300: 300ms;
  --duration-500: 500ms;
  --duration-700: 700ms;
  --duration-1000: 1000ms;

  /* Premium Easing Functions */
  --ease-linear: linear;
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --ease-smooth: cubic-bezier(0.25, 0.46, 0.45, 0.94);

  /* Premium Layout System */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;
  --container-max: 1600px;

  /* Content Width Constraints */
  --content-width-prose: 65ch;
  --content-width-narrow: 480px;
  --content-width-wide: 896px;
}

/* Import Premium Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

/* Premium Dark Mode */
@media (prefers-color-scheme: dark) {
  :root {
    /* Dark Mode Semantic Colors */
    --color-background: var(--color-gray-900);
    --color-surface: var(--color-gray-800);
    --color-surface-elevated: var(--color-gray-700);
    --color-text: var(--color-gray-100);
    --color-text-secondary: var(--color-gray-400);
    --color-text-muted: var(--color-gray-500);
    --color-primary: var(--color-indigo-400);
    --color-primary-hover: var(--color-indigo-300);
    --color-primary-active: var(--color-indigo-500);
    --color-primary-light: rgba(var(--color-indigo-600-rgb), 0.1);
    --color-secondary: var(--color-gray-700);
    --color-secondary-hover: var(--color-gray-600);
    --color-secondary-active: var(--color-gray-500);
    --color-border: var(--color-gray-700);
    --color-border-light: var(--color-gray-800);
    --color-border-strong: var(--color-gray-600);
    --color-btn-primary-text: var(--color-white);
    --color-card-border: var(--color-gray-700);
    --color-card-shadow: rgba(0, 0, 0, 0.25);
    --color-error: var(--color-red-400);
    --color-success: var(--color-emerald-400);
    --color-warning: var(--color-amber-400);
    --color-info: var(--color-blue-400);
    --color-focus-ring: rgba(var(--color-indigo-400-rgb), 0.3);
    --color-select-caret: rgba(var(--color-gray-300-rgb), 0.8);

    /* Dark Mode Gradients */
    --gradient-primary: linear-gradient(135deg, var(--color-indigo-500) 0%, var(--color-blue-500) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--color-gray-800) 0%, var(--color-gray-900) 100%);
    --gradient-hero: linear-gradient(135deg, var(--color-gray-900) 0%, var(--color-gray-800) 50%, var(--color-gray-900) 100%);
    --gradient-card: linear-gradient(145deg, var(--color-gray-800) 0%, var(--color-gray-900) 100%);

    /* Enhanced Dark Mode Shadows */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px -1px rgba(0, 0, 0, 0.4);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -2px rgba(0, 0, 0, 0.4);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -4px rgba(0, 0, 0, 0.4);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 8px 10px -6px rgba(0, 0, 0, 0.4);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.6);
    --shadow-glow: 0 0 0 1px rgba(var(--color-indigo-400-rgb), 0.1), 0 1px 3px 0 rgba(var(--color-indigo-400-rgb), 0.2);
  }
}

/* Manual theme switching data attributes */
[data-color-scheme="dark"] {
  /* Dark Mode Override */
  --color-background: var(--color-gray-900);
  --color-surface: var(--color-gray-800);
  --color-surface-elevated: var(--color-gray-700);
  --color-text: var(--color-gray-100);
  --color-text-secondary: var(--color-gray-400);
  --color-text-muted: var(--color-gray-500);
  --color-primary: var(--color-indigo-400);
  --color-primary-hover: var(--color-indigo-300);
  --color-primary-active: var(--color-indigo-500);
  --color-primary-light: rgba(var(--color-indigo-600-rgb), 0.1);
  --color-secondary: var(--color-gray-700);
  --color-secondary-hover: var(--color-gray-600);
  --color-secondary-active: var(--color-gray-500);
  --color-border: var(--color-gray-700);
  --color-border-light: var(--color-gray-800);
  --color-border-strong: var(--color-gray-600);
  --color-btn-primary-text: var(--color-white);
  --color-card-border: var(--color-gray-700);
  --color-card-shadow: rgba(0, 0, 0, 0.25);
  --color-error: var(--color-red-400);
  --color-success: var(--color-emerald-400);
  --color-warning: var(--color-amber-400);
  --color-info: var(--color-blue-400);
  --color-focus-ring: rgba(var(--color-indigo-400-rgb), 0.3);
  --color-select-caret: rgba(var(--color-gray-300-rgb), 0.8);
}

[data-color-scheme="light"] {
  /* Light Mode Override */
  --color-background: var(--color-white);
  --color-surface: var(--color-white);
  --color-surface-elevated: var(--color-gray-50);
  --color-text: var(--color-gray-900);
  --color-text-secondary: var(--color-gray-600);
  --color-text-muted: var(--color-gray-500);
  --color-primary: var(--color-indigo-600);
  --color-primary-hover: var(--color-indigo-700);
  --color-primary-active: var(--color-indigo-800);
  --color-primary-light: var(--color-indigo-50);
  --color-secondary: var(--color-gray-100);
  --color-secondary-hover: var(--color-gray-200);
  --color-secondary-active: var(--color-gray-300);
  --color-border: var(--color-gray-200);
  --color-border-light: var(--color-gray-100);
  --color-border-strong: var(--color-gray-300);
  --color-btn-primary-text: var(--color-white);
  --color-card-border: var(--color-gray-100);
  --color-card-shadow: rgba(var(--color-gray-900-rgb), 0.05);
  --color-error: var(--color-red-500);
  --color-success: var(--color-emerald-500);
  --color-warning: var(--color-amber-500);
  --color-info: var(--color-blue-500);
  --color-focus-ring: rgba(var(--color-indigo-600-rgb), 0.3);
  --color-select-caret: rgba(var(--color-gray-700-rgb), 0.8);
}

/* Base styles */
html {
  font-size: var(--font-size-base);
  font-family: var(--font-family-base);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-text);
  letter-spacing: var(--letter-spacing-tight);
}

h1 {
  font-size: var(--font-size-4xl);
}
h2 {
  font-size: var(--font-size-3xl);
}
h3 {
  font-size: var(--font-size-2xl);
}
h4 {
  font-size: var(--font-size-xl);
}
h5 {
  font-size: var(--font-size-lg);
}
h6 {
  font-size: var(--font-size-md);
}

p {
  margin: 0 0 var(--space-16) 0;
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

a:hover {
  color: var(--color-primary-hover);
}

code,
pre {
  font-family: var(--font-family-mono);
  font-size: calc(var(--font-size-base) * 0.95);
  background-color: var(--color-secondary);
  border-radius: var(--radius-sm);
}

code {
  padding: var(--space-1) var(--space-4);
}

pre {
  padding: var(--space-16);
  margin: var(--space-16) 0;
  overflow: auto;
  border: 1px solid var(--color-border);
}

pre code {
  background: none;
  padding: 0;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) var(--space-16);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: 500;
  line-height: 1.5;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-standard);
  border: none;
  text-decoration: none;
  position: relative;
}

.btn:focus-visible {
  outline: none;
  box-shadow: var(--focus-ring);
}

.btn--primary {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
}

.btn--primary:hover {
  background: var(--color-primary-hover);
}

.btn--primary:active {
  background: var(--color-primary-active);
}

.btn--secondary {
  background: var(--color-secondary);
  color: var(--color-text);
}

.btn--secondary:hover {
  background: var(--color-secondary-hover);
}

.btn--secondary:active {
  background: var(--color-secondary-active);
}

.btn--outline {
  background: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.btn--outline:hover {
  background: var(--color-secondary);
}

.btn--sm {
  padding: var(--space-4) var(--space-12);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-sm);
}

.btn--lg {
  padding: var(--space-10) var(--space-20);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-md);
}

.btn--full-width {
  width: 100%;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Form elements */
.form-control {
  display: block;
  width: 100%;
  padding: var(--space-8) var(--space-12);
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: var(--color-text);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  transition: border-color var(--duration-fast) var(--ease-standard),
    box-shadow var(--duration-fast) var(--ease-standard);
}

textarea.form-control {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
}

select.form-control {
  padding: var(--space-8) var(--space-12);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: var(--select-caret-light);
  background-repeat: no-repeat;
  background-position: right var(--space-12) center;
  background-size: 16px;
  padding-right: var(--space-32);
}

/* Add a dark mode specific caret */
@media (prefers-color-scheme: dark) {
  select.form-control {
    background-image: var(--select-caret-dark);
  }
}

/* Also handle data-color-scheme */
[data-color-scheme="dark"] select.form-control {
  background-image: var(--select-caret-dark);
}

[data-color-scheme="light"] select.form-control {
  background-image: var(--select-caret-light);
}

.form-control:focus {
  border-color: var(--color-primary);
  outline: var(--focus-outline);
}

.form-label {
  display: block;
  margin-bottom: var(--space-8);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.form-group {
  margin-bottom: var(--space-16);
}

/* Card component */
.card {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: box-shadow var(--duration-normal) var(--ease-standard);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card__body {
  padding: var(--space-16);
}

.card__header,
.card__footer {
  padding: var(--space-16);
  border-bottom: 1px solid var(--color-card-border-inner);
}

/* Status indicators - simplified with CSS variables */
.status {
  display: inline-flex;
  align-items: center;
  padding: var(--space-6) var(--space-12);
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.status--success {
  background-color: rgba(
    var(--color-success-rgb, 33, 128, 141),
    var(--status-bg-opacity)
  );
  color: var(--color-success);
  border: 1px solid
    rgba(var(--color-success-rgb, 33, 128, 141), var(--status-border-opacity));
}

.status--error {
  background-color: rgba(
    var(--color-error-rgb, 192, 21, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-error);
  border: 1px solid
    rgba(var(--color-error-rgb, 192, 21, 47), var(--status-border-opacity));
}

.status--warning {
  background-color: rgba(
    var(--color-warning-rgb, 168, 75, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-warning);
  border: 1px solid
    rgba(var(--color-warning-rgb, 168, 75, 47), var(--status-border-opacity));
}

.status--info {
  background-color: rgba(
    var(--color-info-rgb, 98, 108, 113),
    var(--status-bg-opacity)
  );
  color: var(--color-info);
  border: 1px solid
    rgba(var(--color-info-rgb, 98, 108, 113), var(--status-border-opacity));
}

/* Container layout */
.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: var(--space-16);
  padding-left: var(--space-16);
}

@media (min-width: 640px) {
  .container {
    max-width: var(--container-sm);
  }
}
@media (min-width: 768px) {
  .container {
    max-width: var(--container-md);
  }
}
@media (min-width: 1024px) {
  .container {
    max-width: var(--container-lg);
  }
}
@media (min-width: 1280px) {
  .container {
    max-width: var(--container-xl);
  }
}

/* Utility classes */
.flex {
  display: flex;
}
.flex-col {
  flex-direction: column;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-4 {
  gap: var(--space-4);
}
.gap-8 {
  gap: var(--space-8);
}
.gap-16 {
  gap: var(--space-16);
}

.m-0 {
  margin: 0;
}
.mt-8 {
  margin-top: var(--space-8);
}
.mb-8 {
  margin-bottom: var(--space-8);
}
.mx-8 {
  margin-left: var(--space-8);
  margin-right: var(--space-8);
}
.my-8 {
  margin-top: var(--space-8);
  margin-bottom: var(--space-8);
}

.p-0 {
  padding: 0;
}
.py-8 {
  padding-top: var(--space-8);
  padding-bottom: var(--space-8);
}
.px-8 {
  padding-left: var(--space-8);
  padding-right: var(--space-8);
}
.py-16 {
  padding-top: var(--space-16);
  padding-bottom: var(--space-16);
}
.px-16 {
  padding-left: var(--space-16);
  padding-right: var(--space-16);
}

.block {
  display: block;
}
.hidden {
  display: none;
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

:focus-visible {
  outline: var(--focus-outline);
  outline-offset: 2px;
}

/* Dark mode specifics */
[data-color-scheme="dark"] .btn--outline {
  border: 1px solid var(--color-border-secondary);
}

@font-face {
  font-family: 'FKGroteskNeue';
  src: url('https://r2cdn.perplexity.ai/fonts/FKGroteskNeue.woff2')
    format('woff2');
}

/* END PERPLEXITY DESIGN SYSTEM */
/* Navigation */
.nav {
    background-color: var(--color-surface);
    border-bottom: 1px solid var(--color-border);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: background-color var(--duration-normal) var(--ease-standard);
}

.nav__content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-16) 0;
}

.nav__brand {
    display: flex;
    align-items: center;
    gap: var(--space-8);
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-xl);
}

.nav__logo {
    font-size: var(--font-size-2xl);
}

.nav__title {
    color: var(--color-text);
}

.nav__menu {
    display: flex;
    align-items: center;
    gap: var(--space-32);
}

.nav__link {
    color: var(--color-text-secondary);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    transition: color var(--duration-fast) var(--ease-standard);
    cursor: pointer;
}

.nav__link:hover,
.nav__link.active {
    color: var(--color-primary);
}

.nav__actions {
    display: flex;
    align-items: center;
    gap: var(--space-12);
}

.nav__toggle {
    display: none;
    flex-direction: column;
    gap: var(--space-4);
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--space-8);
}

.nav__toggle span {
    width: 24px;
    height: 2px;
    background-color: var(--color-text);
    transition: all var(--duration-fast) var(--ease-standard);
}

/* Main Content */
.main {
    margin-top: 80px;
}

.page {
    display: none;
}

.page.active {
    display: block;
}

/* Hero Section */
.hero {
    background: var(--color-bg-1);
    padding: var(--space-32) 0 var(--space-32);
    min-height: 600px;
    display: flex;
    align-items: center;
}

.hero__content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.hero__title {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--space-24);
    line-height: var(--line-height-tight);
}

.hero__subtitle {
    font-size: var(--font-size-xl);
    color: var(--color-text-secondary);
    margin-bottom: var(--space-32);
    line-height: var(--line-height-normal);
}

.hero__actions {
    display: flex;
    gap: var(--space-16);
    justify-content: center;
    margin-bottom: var(--space-24);
}

.hero__trust {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-8);
}

.hero__trust-text {
    color: var(--color-text-secondary);
    font-size: var(--font-size-sm);
}

/* Stats Section */
.stats {
    background-color: var(--color-surface);
    padding: var(--space-32) 0;
}

.stats__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-32);
    text-align: center;
}

.stats__item {
    padding: var(--space-24);
}

.stats__number {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-primary);
    margin-bottom: var(--space-8);
}

.stats__label {
    color: var(--color-text-secondary);
    font-weight: var(--font-weight-medium);
}

/* Features Preview */
.features-preview {
    padding: var(--space-32) 0;
}

.section-header {
    text-align: center;
    margin-bottom: var(--space-32);
}

.section-header h2 {
    margin-bottom: var(--space-16);
}

.section-header p {
    color: var(--color-text-secondary);
    font-size: var(--font-size-lg);
}

.features-preview__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-32);
    margin-bottom: var(--space-32);
}

.feature-card {
    background-color: var(--color-surface);
    padding: var(--space-24);
    border-radius: var(--radius-lg);
    border: 1px solid var(--color-card-border);
    text-align: center;
    transition: transform var(--duration-normal) var(--ease-standard);
}

.feature-card:hover {
    transform: translateY(-4px);
}

.feature-card__icon {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--space-16);
}

.feature-card__title {
    margin-bottom: var(--space-12);
}

.feature-card__description {
    color: var(--color-text-secondary);
    margin: 0;
}

.features-preview__cta {
    text-align: center;
}

/* Testimonials */
.testimonials {
    background: var(--color-bg-2);
    padding: var(--space-32) 0;
}

.testimonials__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-32);
}

.testimonial {
    background-color: var(--color-surface);
    padding: var(--space-24);
    border-radius: var(--radius-lg);
    border: 1px solid var(--color-card-border);
}

.testimonial__content {
    margin-bottom: var(--space-16);
}

.testimonial__stars {
    color: #fbbf24;
    margin-bottom: var(--space-12);
    font-size: var(--font-size-lg);
}

.testimonial__quote {
    font-style: italic;
    margin: 0;
}

.testimonial__name {
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--space-4);
}

.testimonial__business {
    color: var(--color-text-secondary);
    font-size: var(--font-size-sm);
}

/* CTA Section */
.cta {
    background: var(--color-bg-3);
    padding: var(--space-32) 0;
    text-align: center;
}

.cta__title {
    margin-bottom: var(--space-16);
}

.cta__subtitle {
    color: var(--color-text-secondary);
    font-size: var(--font-size-lg);
    margin-bottom: var(--space-24);
}

.cta__actions {
    display: flex;
    gap: var(--space-16);
    justify-content: center;
}

/* Page Header */
.page-header {
    background: var(--color-bg-1);
    padding: var(--space-32) 0;
    text-align: center;
}

.page-header h1 {
    margin-bottom: var(--space-16);
}

.page-header p {
    color: var(--color-text-secondary);
    font-size: var(--font-size-lg);
    margin: 0;
}

/* Features Detailed */
.features-detailed {
    padding: var(--space-32) 0;
}

.features-detailed__grid {
    display: grid;
    gap: var(--space-32);
}

.feature-detailed {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: var(--space-24);
    padding: var(--space-24);
    background-color: var(--color-surface);
    border-radius: var(--radius-lg);
    border: 1px solid var(--color-card-border);
}

.feature-detailed__icon {
    font-size: var(--font-size-4xl);
    display: flex;
    align-items: flex-start;
    justify-content: center;
    width: 80px;
}

.feature-detailed__content h3 {
    margin-bottom: var(--space-12);
}

.feature-detailed__content p {
    color: var(--color-text-secondary);
    margin-bottom: var(--space-16);
}

.feature-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.feature-list li {
    padding: var(--space-4) 0;
    color: var(--color-text-secondary);
}

.feature-list li:before {
    content: "✓ ";
    color: var(--color-success);
    font-weight: var(--font-weight-bold);
    margin-right: var(--space-8);
}

/* Industries */
.industries {
    background: var(--color-bg-4);
    padding: var(--space-32) 0;
}

.industries__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-16);
}

.industry-item {
    background-color: var(--color-surface);
    padding: var(--space-16);
    border-radius: var(--radius-base);
    text-align: center;
    font-weight: var(--font-weight-medium);
    border: 1px solid var(--color-card-border);
}

/* Pricing */
.pricing {
    padding: var(--space-32) 0;
}

.pricing__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-32);
    align-items: stretch;
}

.pricing-card {
    background-color: var(--color-surface);
    border: 1px solid var(--color-card-border);
    border-radius: var(--radius-lg);
    padding: var(--space-24);
    position: relative;
    display: flex;
    flex-direction: column;
}

.pricing-card--popular {
    border-color: var(--color-primary);
    box-shadow: var(--shadow-lg);
    transform: scale(1.05);
}

.pricing-card__badge {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--color-primary);
    color: var(--color-btn-primary-text);
    padding: var(--space-4) var(--space-16);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-bold);
}

.pricing-card__header {
    text-align: center;
    margin-bottom: var(--space-24);
}

.pricing-card__name {
    margin-bottom: var(--space-16);
}

.pricing-card__price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    margin-bottom: var(--space-12);
}

.pricing-card__currency {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-text-secondary);
}

.pricing-card__amount {
    font-size: 3rem;
    font-weight: var(--font-weight-bold);
    color: var(--color-primary);
}

.pricing-card__period {
    font-size: var(--font-size-lg);
    color: var(--color-text-secondary);
}

.pricing-card__description {
    color: var(--color-text-secondary);
    margin: 0;
}

.pricing-card__features {
    flex: 1;
    margin-bottom: var(--space-24);
}

.pricing-feature {
    padding: var(--space-8) 0;
    border-bottom: 1px solid var(--color-card-border-inner);
}

.pricing-feature:last-child {
    border-bottom: none;
}

/* FAQ */
.faq {
    background: var(--color-bg-5);
    padding: var(--space-32) 0;
}

.faq__items {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    border-bottom: 1px solid var(--color-border);
}

.faq-item__question {
    width: 100%;
    background: none;
    border: none;
    padding: var(--space-20) 0;
    text-align: left;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    color: var(--color-text);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.faq-item__arrow {
    font-size: var(--font-size-2xl);
    color: var(--color-primary);
    transition: transform var(--duration-fast) var(--ease-standard);
}

.faq-item.active .faq-item__arrow {
    transform: rotate(45deg);
}

.faq-item__answer {
    padding: 0 0 var(--space-20) 0;
    color: var(--color-text-secondary);
    display: none;
    animation: fadeIn var(--duration-normal) var(--ease-standard);
}

.faq-item.active .faq-item__answer {
    display: block;
}

/* About Content */
.about-content {
    padding: var(--space-32) 0;
}

.about-story,
.about-mission {
    max-width: 800px;
    margin: 0 auto var(--space-32) auto;
}

.about-story h2,
.about-mission h2 {
    margin-bottom: var(--space-16);
}

.about-story p,
.about-mission p {
    color: var(--color-text-secondary);
    margin-bottom: var(--space-16);
}

.about-values h2 {
    text-align: center;
    margin-bottom: var(--space-32);
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-32);
}

.value-item {
    text-align: center;
    padding: var(--space-24);
    background-color: var(--color-surface);
    border-radius: var(--radius-lg);
    border: 1px solid var(--color-card-border);
}

.value-item__icon {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--space-16);
}

.value-item h3 {
    margin-bottom: var(--space-12);
}

.value-item p {
    color: var(--color-text-secondary);
    margin: 0;
}

/* Contact Content */
.contact-content {
    padding: var(--space-32) 0;
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-32);
}

.contact-info h2,
.contact-form h2 {
    margin-bottom: var(--space-24);
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: var(--space-16);
    margin-bottom: var(--space-24);
}

.contact-item__icon {
    font-size: var(--font-size-xl);
    margin-top: var(--space-4);
}

.contact-item__label {
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--space-4);
}

.contact-item__value {
    color: var(--color-text-secondary);
}

.contact-hours {
    margin-top: var(--space-32);
}

.contact-hours h3 {
    margin-bottom: var(--space-16);
}

.contact-hours__item {
    display: flex;
    justify-content: space-between;
    padding: var(--space-8) 0;
    border-bottom: 1px solid var(--color-card-border-inner);
}

.contact-hours__item:last-child {
    border-bottom: none;
}

.contact-form {
    background-color: var(--color-surface);
    padding: var(--space-24);
    border-radius: var(--radius-lg);
    border: 1px solid var(--color-card-border);
}

/* Dashboard Preview */
.dashboard-preview {
    padding: var(--space-32) 0;
}

.dashboard-mockup {
    background-color: var(--color-surface);
    border-radius: var(--radius-lg);
    border: 1px solid var(--color-card-border);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.dashboard-header {
    background: var(--color-bg-6);
    padding: var(--space-16);
}

.dashboard-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dashboard-logo {
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-lg);
}

.dashboard-user {
    background-color: var(--color-surface);
    padding: var(--space-8) var(--space-12);
    border-radius: var(--radius-base);
    font-size: var(--font-size-sm);
}

.dashboard-content {
    display: grid;
    grid-template-columns: 250px 1fr;
    min-height: 500px;
}

.dashboard-sidebar {
    background: var(--color-bg-7);
    padding: var(--space-16);
}

.dashboard-menu-item {
    padding: var(--space-12);
    margin-bottom: var(--space-8);
    border-radius: var(--radius-base);
    cursor: pointer;
    transition: background-color var(--duration-fast) var(--ease-standard);
}

.dashboard-menu-item.active {
    background-color: var(--color-primary);
    color: var(--color-btn-primary-text);
}

.dashboard-menu-item:hover:not(.active) {
    background-color: var(--color-secondary);
}

.dashboard-main {
    padding: var(--space-24);
}

.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--space-16);
    margin-bottom: var(--space-32);
}

.dashboard-stat {
    background-color: var(--color-surface);
    padding: var(--space-16);
    border-radius: var(--radius-base);
    text-align: center;
    border: 1px solid var(--color-card-border);
}

.dashboard-stat__number {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-primary);
    margin-bottom: var(--space-4);
}

.dashboard-stat__label {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
}

.dashboard-charts {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-24);
}

.dashboard-chart {
    background-color: var(--color-surface);
    padding: var(--space-16);
    border-radius: var(--radius-base);
    border: 1px solid var(--color-card-border);
}

.dashboard-chart h3 {
    margin-bottom: var(--space-16);
    font-size: var(--font-size-lg);
}

.chart-placeholder {
    background: var(--color-bg-8);
    padding: var(--space-32);
    border-radius: var(--radius-base);
    text-align: center;
    color: var(--color-text-secondary);
    font-style: italic;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal.hidden {
    display: none;
}

.modal__overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal__content {
    position: relative;
    background-color: var(--color-surface);
    border-radius: var(--radius-lg);
    padding: var(--space-24);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
}

.modal__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-24);
}

.modal__header h2 {
    margin: 0;
}

.modal__close {
    background: none;
    border: none;
    font-size: var(--font-size-2xl);
    cursor: pointer;
    color: var(--color-text-secondary);
    padding: var(--space-4);
}

.modal__close:hover {
    color: var(--color-text);
}

.modal__footer {
    text-align: center;
    margin-top: var(--space-16);
}

.modal__footer p {
    margin: 0;
    color: var(--color-text-secondary);
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in {
    animation: fadeIn var(--duration-normal) var(--ease-standard);
}

.animate-slide-up {
    animation: slideUp var(--duration-normal) var(--ease-standard);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav__menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background-color: var(--color-surface);
        border-top: 1px solid var(--color-border);
        flex-direction: column;
        padding: var(--space-16);
        gap: var(--space-16);
    }

    .nav__menu.active {
        display: flex;
    }

    .nav__toggle {
        display: flex;
    }

    .nav__actions {
        display: none;
    }

    .hero__title {
        font-size: var(--font-size-3xl);
    }

    .hero__subtitle {
        font-size: var(--font-size-lg);
    }

    .hero__actions {
        flex-direction: column;
        align-items: center;
    }

    .stats__grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-16);
    }

    .features-preview__grid {
        grid-template-columns: 1fr;
    }

    .testimonials__grid {
        grid-template-columns: 1fr;
    }

    .cta__actions {
        flex-direction: column;
        align-items: center;
    }

    .pricing__grid {
        grid-template-columns: 1fr;
    }

    .pricing-card--popular {
        transform: none;
    }

    .contact-grid {
        grid-template-columns: 1fr;
    }

    .dashboard-content {
        grid-template-columns: 1fr;
    }

    .dashboard-sidebar {
        display: none;
    }

    .dashboard-charts {
        grid-template-columns: 1fr;
    }

    .feature-detailed {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .feature-detailed__icon {
        justify-content: center;
        width: 100%;
    }

    .values-grid {
        grid-template-columns: 1fr;
    }

    .industries__grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .stats__grid {
        grid-template-columns: 1fr;
    }

    .industries__grid {
        grid-template-columns: 1fr;
    }

    .modal__content {
        width: 95%;
        padding: var(--space-16);
    }
}