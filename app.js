// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all functionality
    initNavigation();
    initModals();
    initButtons();
    initForms();
    initFAQ();
    initAnimations();
    console.log('HyperLocal SEO app initialized');
});

// Navigation functionality
function initNavigation() {
    // Get navigation elements
    const navToggle = document.getElementById('navToggle');
    const navMenu = document.getElementById('navMenu');
    const navLinks = document.querySelectorAll('[data-page]');
    const pages = document.querySelectorAll('.page');
    
    // Mobile menu toggle
    if (navToggle && navMenu) {
        navToggle.addEventListener('click', function(e) {
            e.preventDefault();
            navMenu.classList.toggle('active');
            navToggle.classList.toggle('active');
        });
    }
    
    // Page navigation
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetPage = this.getAttribute('data-page');
            
            if (targetPage) {
                // Hide all pages
                pages.forEach(page => page.classList.remove('active'));
                
                // Show target page
                const targetElement = document.getElementById(targetPage);
                if (targetElement) {
                    targetElement.classList.add('active');
                    
                    // Update active nav link
                    document.querySelectorAll('.nav__link').forEach(navLink => {
                        navLink.classList.remove('active');
                    });
                    
                    // Find and activate the corresponding nav link
                    const correspondingNavLink = document.querySelector(`.nav__link[data-page="${targetPage}"]`);
                    if (correspondingNavLink) {
                        correspondingNavLink.classList.add('active');
                    }
                    
                    // Scroll to top
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                    
                    // Close mobile menu
                    if (navMenu) navMenu.classList.remove('active');
                    if (navToggle) navToggle.classList.remove('active');
                    
                    // Trigger animations for home page
                    if (targetPage === 'home') {
                        setTimeout(() => animateStats(), 500);
                    }
                }
            }
        });
    });
    
    console.log('Navigation initialized');
}

// Modal functionality
function initModals() {
    const loginModal = document.getElementById('loginModal');
    const signupModal = document.getElementById('signupModal');
    
    // Modal controls
    const loginBtn = document.getElementById('loginBtn');
    const signupBtn = document.getElementById('signupBtn');
    const heroTrialBtn = document.getElementById('heroTrialBtn');
    const ctaTrialBtn = document.getElementById('ctaTrialBtn');
    
    const loginClose = document.getElementById('loginModalClose');
    const signupClose = document.getElementById('signupModalClose');
    
    const showSignupLink = document.getElementById('showSignup');
    const showLoginLink = document.getElementById('showLogin');
    
    // Show login modal
    if (loginBtn && loginModal) {
        loginBtn.addEventListener('click', function(e) {
            e.preventDefault();
            showModal(loginModal);
        });
    }
    
    // Show signup modal
    const signupButtons = [signupBtn, heroTrialBtn, ctaTrialBtn];
    signupButtons.forEach(btn => {
        if (btn && signupModal) {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                showModal(signupModal);
            });
        }
    });
    
    // Close modals
    if (loginClose && loginModal) {
        loginClose.addEventListener('click', () => hideModal(loginModal));
    }
    
    if (signupClose && signupModal) {
        signupClose.addEventListener('click', () => hideModal(signupModal));
    }
    
    // Switch between modals
    if (showSignupLink && signupModal && loginModal) {
        showSignupLink.addEventListener('click', function(e) {
            e.preventDefault();
            hideModal(loginModal);
            showModal(signupModal);
        });
    }
    
    if (showLoginLink && loginModal && signupModal) {
        showLoginLink.addEventListener('click', function(e) {
            e.preventDefault();
            hideModal(signupModal);
            showModal(loginModal);
        });
    }
    
    // Close on overlay click
    [loginModal, signupModal].forEach(modal => {
        if (modal) {
            const overlay = modal.querySelector('.modal__overlay');
            if (overlay) {
                overlay.addEventListener('click', function(e) {
                    if (e.target === this) {
                        hideModal(modal);
                    }
                });
            }
        }
    });
    
    // Close on Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            hideModal(loginModal);
            hideModal(signupModal);
        }
    });
    
    console.log('Modals initialized');
}

// Show modal function
function showModal(modal) {
    if (modal) {
        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    }
}

// Hide modal function
function hideModal(modal) {
    if (modal) {
        modal.classList.add('hidden');
        document.body.style.overflow = '';
    }
}

// Button functionality
function initButtons() {
    // Get all buttons and links
    const allButtons = document.querySelectorAll('button, .btn');
    
    allButtons.forEach(button => {
        const text = button.textContent.trim().toLowerCase();
        
        // Skip if already has event listener
        if (button.hasAttribute('data-initialized')) return;
        
        // Start Free Trial buttons
        if (text.includes('start free trial') || text.includes('free trial')) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const signupModal = document.getElementById('signupModal');
                showModal(signupModal);
            });
            button.setAttribute('data-initialized', 'true');
        }
        
        // Contact Sales buttons
        else if (text.includes('contact sales')) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                navigateToPage('contact');
            });
            button.setAttribute('data-initialized', 'true');
        }
        
        // See Demo buttons
        else if (text.includes('see demo')) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                navigateToPage('dashboard');
            });
            button.setAttribute('data-initialized', 'true');
        }
        
        // View All Features buttons
        else if (text.includes('view all features')) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                navigateToPage('features');
            });
            button.setAttribute('data-initialized', 'true');
        }
    });
    
    console.log('Buttons initialized');
}

// Helper function for navigation
function navigateToPage(pageId) {
    const pages = document.querySelectorAll('.page');
    const targetPage = document.getElementById(pageId);
    
    if (targetPage) {
        // Hide all pages
        pages.forEach(page => page.classList.remove('active'));
        
        // Show target page
        targetPage.classList.add('active');
        
        // Update nav links
        document.querySelectorAll('.nav__link').forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('data-page') === pageId) {
                link.classList.add('active');
            }
        });
        
        // Scroll to top
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }
}

// Form functionality
function initForms() {
    // Contact form
    const contactForm = document.getElementById('contactForm');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            showNotification('Thank you for your message! We\'ll get back to you soon.', 'success');
            this.reset();
        });
    }
    
    // Login form
    const loginModal = document.getElementById('loginModal');
    if (loginModal) {
        const loginForm = loginModal.querySelector('form');
        if (loginForm) {
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                showNotification('Login successful! Redirecting to dashboard...', 'success');
                setTimeout(() => {
                    hideModal(loginModal);
                    navigateToPage('dashboard');
                }, 1500);
            });
        }
    }
    
    // Signup form
    const signupModal = document.getElementById('signupModal');
    if (signupModal) {
        const signupForm = signupModal.querySelector('form');
        if (signupForm) {
            signupForm.addEventListener('submit', function(e) {
                e.preventDefault();
                showNotification('Account created successfully! Starting your free trial...', 'success');
                setTimeout(() => {
                    hideModal(signupModal);
                    navigateToPage('dashboard');
                }, 1500);
            });
        }
    }
    
    console.log('Forms initialized');
}

// FAQ functionality
function initFAQ() {
    const faqItems = document.querySelectorAll('.faq-item');
    
    faqItems.forEach(item => {
        const question = item.querySelector('.faq-item__question');
        if (question) {
            question.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Close other items
                faqItems.forEach(otherItem => {
                    if (otherItem !== item) {
                        otherItem.classList.remove('active');
                    }
                });
                
                // Toggle current item
                item.classList.toggle('active');
            });
        }
    });
    
    console.log('FAQ initialized');
}

// Animation functionality
function initAnimations() {
    // Stats animation on page load for home
    const currentPage = document.querySelector('.page.active');
    if (currentPage && currentPage.id === 'home') {
        setTimeout(() => animateStats(), 1000);
    }
    
    // Intersection Observer for scroll animations
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-slide-up');
            }
        });
    }, { threshold: 0.1 });
    
    // Observe animated elements
    const animatedElements = document.querySelectorAll('.feature-card, .testimonial, .pricing-card, .value-item');
    animatedElements.forEach(el => observer.observe(el));
    
    console.log('Animations initialized');
}

// Stats counter animation
function animateStats() {
    const statsNumbers = document.querySelectorAll('.stats__number');
    
    statsNumbers.forEach(numberElement => {
        const target = parseFloat(numberElement.getAttribute('data-target'));
        const increment = target / 100;
        let current = 0;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            
            // Format number display
            let displayValue;
            if (target >= 1000) {
                displayValue = Math.floor(current).toLocaleString();
            } else if (target < 1) {
                displayValue = current.toFixed(2);
            } else {
                displayValue = Math.floor(current);
            }
            
            numberElement.textContent = displayValue;
        }, 20);
    });
}

// Notification system
function showNotification(message, type = 'info') {
    // Remove existing notifications
    document.querySelectorAll('.notification').forEach(n => n.remove());
    
    // Create notification
    const notification = document.createElement('div');
    notification.className = `notification notification--${type}`;
    notification.innerHTML = `
        <div style="display: flex; align-items: center; justify-content: space-between; gap: 12px;">
            <span style="color: var(--color-text); font-weight: 500;">${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; font-size: 18px; cursor: pointer; color: var(--color-text-secondary); padding: 4px;">&times;</button>
        </div>
    `;
    
    // Style notification
    Object.assign(notification.style, {
        position: 'fixed',
        top: '100px',
        right: '20px',
        zIndex: '3000',
        background: 'var(--color-surface)',
        border: '1px solid var(--color-border)',
        borderRadius: '8px',
        padding: '16px',
        boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
        maxWidth: '400px',
        animation: 'slideInRight 0.3s ease-out'
    });
    
    // Type-specific styling
    if (type === 'success') {
        notification.style.borderColor = 'var(--color-success)';
        notification.style.backgroundColor = 'rgba(33, 128, 141, 0.1)';
    }
    
    document.body.appendChild(notification);
    
    // Auto remove
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Add required CSS for animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    .animate-slide-up {
        animation: slideUp 0.6s ease-out;
    }
    
    @keyframes slideUp {
        from { opacity: 0; transform: translateY(30px); }
        to { opacity: 1; transform: translateY(0); }
    }
`;
document.head.appendChild(style);

// Export functions for external use
window.HyperLocalSEO = {
    navigateToPage,
    showModal,
    hideModal,
    showNotification
};